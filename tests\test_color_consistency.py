"""
Tests pour vérifier la cohérence des couleurs dans l'exportation des masques.
"""
import unittest
import numpy as np
import tempfile
import os
from PIL import Image
import cv2

from config.constants import MASK_COLORS_RGB, MASK_COLORS_BGR, CLASS_MAP
from services.export_service import save_unified_masks
from models.mask_model import MaskModel


class TestColorConsistency(unittest.TestCase):
    """Tests de cohérence des couleurs."""
    
    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Créer un masque de test avec toutes les classes
        self.test_mask = np.zeros((100, 100), dtype=np.uint8)
        self.test_mask[10:30, 10:30] = 1  # frontwall
        self.test_mask[40:60, 10:30] = 2  # backwall
        self.test_mask[10:30, 40:60] = 3  # flaw
        self.test_mask[40:60, 40:60] = 4  # indication
        
        # Paramètres de test
        self.label_settings = {
            'frontwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'backwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'flaw': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'indication': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False}
        }
        
        # Créer un modèle de test minimal
        self.model = MaskModel(class_map=CLASS_MAP)
    
    def tearDown(self):
        """Nettoyage après les tests."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_exported_visual_mask_colors(self):
        """Test que les couleurs du masque visuel exporté sont correctes."""
        # Exporter le masque
        save_unified_masks(
            self.test_mask, 
            self.temp_dir, 
            "test_image.png", 
            self.label_settings, 
            self.model
        )
        
        # Charger le masque visuel exporté
        visual_path = os.path.join(self.temp_dir, "masks_visual", "test_image.png")
        self.assertTrue(os.path.exists(visual_path), "Le masque visuel n'a pas été créé")
        
        exported_image = np.array(Image.open(visual_path))
        
        # Vérifier les couleurs pour chaque classe
        for class_value, expected_rgb in MASK_COLORS_RGB.items():
            # Trouver les pixels de cette classe dans le masque original
            class_pixels = (self.test_mask == class_value)
            
            if np.any(class_pixels):
                # Vérifier que les pixels correspondants ont la bonne couleur
                exported_colors = exported_image[class_pixels]
                
                # Tous les pixels de cette classe doivent avoir la même couleur
                unique_colors = np.unique(exported_colors.reshape(-1, 3), axis=0)
                self.assertEqual(len(unique_colors), 1, 
                               f"Couleur incohérente pour la classe {class_value}")
                
                # La couleur doit correspondre à celle attendue
                actual_color = unique_colors[0]
                np.testing.assert_array_equal(
                    actual_color, expected_rgb,
                    f"Couleur incorrecte pour la classe {class_value}. "
                    f"Attendu: {expected_rgb}, Obtenu: {actual_color}"
                )
    
    def test_binary_mask_values(self):
        """Test que le masque binaire contient les bonnes valeurs."""
        # Exporter le masque
        save_unified_masks(
            self.test_mask, 
            self.temp_dir, 
            "test_image.png", 
            self.label_settings, 
            self.model
        )
        
        # Charger le masque binaire exporté
        binary_path = os.path.join(self.temp_dir, "masks_binary", "test_image.png")
        self.assertTrue(os.path.exists(binary_path), "Le masque binaire n'a pas été créé")
        
        exported_mask = np.array(Image.open(binary_path))
        
        # Vérifier que les valeurs sont préservées
        np.testing.assert_array_equal(
            exported_mask, self.test_mask,
            "Le masque binaire exporté ne correspond pas au masque original"
        )
    
    def test_color_constants_consistency(self):
        """Test que les constantes de couleurs sont cohérentes entre BGR et RGB."""
        for class_value in CLASS_MAP.values():
            if class_value in MASK_COLORS_BGR and class_value in MASK_COLORS_RGB:
                bgr = MASK_COLORS_BGR[class_value]
                rgb = MASK_COLORS_RGB[class_value]
                
                # Vérifier que RGB = BGR inversé
                expected_rgb = [bgr[2], bgr[1], bgr[0]]  # BGR -> RGB
                self.assertEqual(
                    rgb, expected_rgb,
                    f"Incohérence BGR/RGB pour la classe {class_value}. "
                    f"BGR: {bgr}, RGB: {rgb}, RGB attendu: {expected_rgb}"
                )
    
    def test_all_classes_have_colors(self):
        """Test que toutes les classes ont des couleurs définies."""
        for label, class_value in CLASS_MAP.items():
            self.assertIn(class_value, MASK_COLORS_BGR, 
                         f"Couleur BGR manquante pour {label} (valeur {class_value})")
            self.assertIn(class_value, MASK_COLORS_RGB, 
                         f"Couleur RGB manquante pour {label} (valeur {class_value})")


if __name__ == '__main__':
    unittest.main()
