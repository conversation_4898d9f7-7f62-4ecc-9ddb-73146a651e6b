# === model/mask_exporter.py ===
import os
import numpy as np
from PIL import Image
import cv2
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
from utils.helpers import normalize_polygon_coords, apply_binary_threshold, remove_isolated_columns
from utils.morphology import smooth_mask_contours
from services.profile_service import generate_profile_mask
from .json_service import JsonExporter, generate_json_filename
from config.constants import MASK_COLORS_RGB, MASK_COLORS_BGR

def export_masks(model, threshold, mask_type="standard", smooth_enabled=False, smooth_radius=0):
    """
    Exporte les masques avec le seuil, le type et l'arrondissement spécifiés.

    Args:
        model: Modèle contenant les données
        threshold: Seuil de binarisation
        mask_type: Type de masque ("standard" ou "polygon")
        smooth_enabled: Activer l'arrondissement des contours
        smooth_radius: Rayon d'arrondissement (si activé)
    """
    gray = cv2.cvtColor(model.image_original, cv2.COLOR_BGR2GRAY)
    binary_mask = apply_binary_threshold(gray, threshold)
    h, w = binary_mask.shape
    mask_std = np.zeros((h, w), dtype=np.uint8)
    mask_poly = np.zeros((h, w), dtype=np.uint8)

    for label, val in {'frontwall': 1, 'backwall': 2, 'flaw': 3, 'indication': 4}.items():
        for pts in model.polygons[label]:
            if len(pts) >= 3:
                pts_corr = normalize_polygon_coords(pts, model.margin)
                tmp = np.zeros_like(mask_std, dtype=np.uint8)
                cv2.fillPoly(tmp, [np.array(pts_corr, dtype=np.int32)], 1)
                tmp &= binary_mask
                mask_std[tmp > 0] = val
                # Pour le masque polygonal, générer des profils pour TOUS les types (frontwall, backwall, flaw, indication)
                mask_poly = np.maximum(mask_poly, generate_profile_mask(tmp, val))

    # Appliquer l'arrondissement des contours si activé
    if smooth_enabled and smooth_radius > 0:
        mask_std = smooth_mask_contours(mask_std, kernel_size=int(2 * smooth_radius + 1))
        mask_poly = smooth_mask_contours(mask_poly, kernel_size=int(2 * smooth_radius + 1))

    base = os.path.dirname(model.image_list[model.current_index])
    name = os.path.basename(model.image_list[model.current_index])

    # Choisir le masque à exporter selon le type sélectionné
    save_all_masks(mask_std, mask_poly, base, name, mask_type)


def save_all_masks(m1, m2, folder, fname, mask_type="standard"):
    """
    Sauvegarde les masques selon le type sélectionné.

    Args:
        m1: Masque standard
        m2: Masque polygonal
        folder: Dossier de destination
        fname: Nom du fichier
        mask_type: Type de masque à privilégier ("standard" ou "polygon")
    """
    # Créer les dossiers selon le type sélectionné
    if mask_type == "polygon":
        selected_mask = m2
        print(f"✅ POLYGON masks exported: {fname}")
    else:  # standard
        selected_mask = m1
        print(f"✅ STANDARD masks exported: {fname}")

    # Créer aussi les dossiers pour l'autre type (pour compatibilité)
    std_dir = os.path.join(folder, "masks_standard")
    poly_dir = os.path.join(folder, "masks_polygon")
    vis_poly_dir = os.path.join(folder, "masks_polygon_visual")
    vis_std_dir = os.path.join(folder, "masks_standard_visual")

    os.makedirs(std_dir, exist_ok=True)
    os.makedirs(poly_dir, exist_ok=True)
    os.makedirs(vis_poly_dir, exist_ok=True)
    os.makedirs(vis_std_dir, exist_ok=True)

    # Sauvegarder les deux types pour compatibilité
    Image.fromarray(m1).save(os.path.join(std_dir, fname))
    Image.fromarray(m2).save(os.path.join(poly_dir, fname))

    # Créer les versions visuelles avec les mêmes couleurs BGR que dans l'interface
    rgb_poly = np.zeros((*m2.shape, 3), dtype=np.uint8)
    rgb_poly[m2 == 1] = [255, 0, 0]  # Frontwall en bleu (BGR)
    rgb_poly[m2 == 2] = [0, 255, 0]  # Backwall en vert (BGR)
    rgb_poly[m2 == 3] = [0, 0, 255]  # Flaw en rouge (BGR)
    rgb_poly[m2 == 4] = [0, 165, 255]  # Indication en orange (BGR)
    Image.fromarray(rgb_poly).save(os.path.join(vis_poly_dir, fname))

    rgb_std = np.zeros((*m1.shape, 3), dtype=np.uint8)
    rgb_std[m1 == 1] = [255, 0, 0]  # Frontwall en bleu (BGR)
    rgb_std[m1 == 2] = [0, 255, 0]  # Backwall en vert (BGR)
    rgb_std[m1 == 3] = [0, 0, 255]  # Flaw en rouge (BGR)
    rgb_std[m1 == 4] = [0, 165, 255]  # Indication en orange (BGR)
    Image.fromarray(rgb_std).save(os.path.join(vis_std_dir, fname))

    # Afficher les statistiques du masque sélectionné
    total_pixels = selected_mask.size
    frontwall_pixels = np.sum(selected_mask == 1)
    backwall_pixels = np.sum(selected_mask == 2)
    flaw_pixels = np.sum(selected_mask == 3)
    indication_pixels = np.sum(selected_mask == 4)
    print(f"   Type: {mask_type} | Frontwall: {frontwall_pixels} | Backwall: {backwall_pixels} | Flaw: {flaw_pixels} | Indication: {indication_pixels} pixels")


def export_individual_masks(model, label_settings):
    """
    Exporte un masque unifié avec les paramètres individuels de chaque label.
    Génère un masque binaire, un masque visible ET un fichier JSON selon les choix de l'utilisateur.

    Args:
        model: Modèle contenant les données
        label_settings: Dictionnaire des paramètres par label
    """
    h, w = model.image_original.shape[:2]
    final_mask = np.zeros((h, w), dtype=np.uint8)

    # Traiter chaque label avec ses propres paramètres
    for label, val in {'frontwall': 1, 'backwall': 2, 'flaw': 3, 'indication': 4}.items():
        settings = label_settings[label]
        threshold = settings["threshold"]
        mask_type = settings["mask_type"]
        smooth_enabled = settings["smooth_contours"]

        # Calculer le masque binaire avec le threshold spécifique
        gray = cv2.cvtColor(model.image_original, cv2.COLOR_BGR2GRAY)
        binary_mask = apply_binary_threshold(gray, threshold)

        # Créer le masque temporaire pour ce label
        label_mask = np.zeros((h, w), dtype=np.uint8)

        # Traiter les polygones de ce label
        for pts in model.polygons[label]:
            if len(pts) >= 3:
                pts_corr = normalize_polygon_coords(pts, model.margin)
                tmp = np.zeros_like(label_mask, dtype=np.uint8)
                cv2.fillPoly(tmp, [np.array(pts_corr, dtype=np.int32)], 1)
                tmp &= binary_mask

                # Appliquer le type de masque choisi pour ce label
                if mask_type == "standard":
                    # Masque standard : utiliser directement les polygones
                    label_mask[tmp > 0] = val
                else:  # polygon
                    # Masque polygonal : générer des profils automatiques
                    profile_mask = generate_profile_mask(tmp, val)
                    label_mask = np.maximum(label_mask, profile_mask)

        # Appliquer l'arrondissement spécifique à ce label
        if smooth_enabled and np.any(label_mask == val):
            # Extraire seulement les pixels de ce label
            label_binary = (label_mask == val).astype(np.uint8)
            smoothed = smooth_mask_contours(label_binary * val, 0.5)
            label_mask[label_mask == val] = 0  # Effacer l'ancien
            label_mask[smoothed == val] = val  # Ajouter le nouveau

        # Ajouter ce label au masque final
        final_mask = np.maximum(final_mask, label_mask)

    # Sauvegarder le masque unifié
    base = os.path.dirname(model.image_list[model.current_index])
    name = os.path.basename(model.image_list[model.current_index])

    # Sauvegarder selon vos choix (passer le modèle pour l'export JSON)
    save_unified_masks(final_mask, base, name, label_settings, model)


def save_unified_masks(mask, folder, fname, label_settings, model):
    """
    Sauvegarde un masque unifié avec paramètres individuels dans 3 dossiers séparés.
    Génère un masque binaire, un masque visible ET un fichier JSON selon les choix utilisateur.

    Args:
        mask: Masque unifié avec tous les labels
        folder: Dossier de destination
        fname: Nom du fichier
        label_settings: Paramètres utilisés pour chaque label
        model: Modèle contenant les données pour l'export JSON
    """
    # Créer 3 dossiers séparés
    binary_dir = os.path.join(folder, "masks_binary")
    visual_dir = os.path.join(folder, "masks_visual")
    json_dir = os.path.join(folder, "json")

    os.makedirs(binary_dir, exist_ok=True)
    os.makedirs(visual_dir, exist_ok=True)
    os.makedirs(json_dir, exist_ok=True)

    # 1. MASQUE BINAIRE : Masque avec valeurs 1, 2, 3, 4 pour frontwall, backwall, flaw, indication
    binary_path = os.path.join(binary_dir, fname)
    Image.fromarray(mask).save(binary_path)

    # 2. MASQUE VISIBLE : Version colorée pour visualisation
    visual_mask = np.zeros((*mask.shape, 3), dtype=np.uint8)
    # Utiliser les couleurs RGB pour l'export PIL
    for class_value, rgb_color in MASK_COLORS_RGB.items():
        visual_mask[mask == class_value] = rgb_color

    # Sauvegarder le masque visible (même nom de fichier)
    visual_path = os.path.join(visual_dir, fname)
    Image.fromarray(visual_mask).save(visual_path)

    # Afficher les informations d'export
    print(f"✅ UNIFIED masks exported: {fname}")
    print(f"   📁 Masque binaire: {binary_dir}")
    print(f"   📁 Masque visible: {visual_dir}")
    print(f"   📁 Dossier JSON: {json_dir}")
    print(f"   🔢 Fichier binaire: {fname}")
    print(f"   🎨 Fichier visible: {fname}")
    print()

    # Afficher les paramètres utilisés pour chaque label
    print("   Paramètres individuels utilisés:")
    for label, settings in label_settings.items():
        pixels = np.sum(mask == {'frontwall': 1, 'backwall': 2, 'flaw': 3, 'indication': 4}[label])
        color = {'frontwall': 'bleu', 'backwall': 'vert', 'flaw': 'rouge', 'indication': 'orange'}[label]
        print(f"   • {label.upper()} ({color}): threshold={settings['threshold']}, "
              f"type={settings['mask_type']}, smooth={settings['smooth_contours']}, "
              f"pixels={pixels}")

    # Statistiques globales
    frontwall_pixels = np.sum(mask == 1)
    backwall_pixels = np.sum(mask == 2)
    flaw_pixels = np.sum(mask == 3)
    indication_pixels = np.sum(mask == 4)
    total_annotated = frontwall_pixels + backwall_pixels + flaw_pixels + indication_pixels
    total_image = mask.size
    coverage = (total_annotated / total_image) * 100

    print()
    print(f"   📊 Statistiques globales:")
    print(f"   • Frontwall: {frontwall_pixels} pixels")
    print(f"   • Backwall: {backwall_pixels} pixels")
    print(f"   • Flaw: {flaw_pixels} pixels")
    print(f"   • Indication: {indication_pixels} pixels")
    print(f"   • Total annoté: {total_annotated} pixels ({coverage:.1f}% de l'image)")

    # 3. EXPORT JSON AUTOMATIQUE avec le même nom que l'image
    print()
    print(f"   📄 Export JSON automatique...")
    try:
        # Obtenir le nom de l'image actuelle (sans extension)
        image_name = os.path.splitext(fname)[0]
        json_filename = f"{image_name}.json"
        json_path = os.path.join(json_dir, json_filename)

        # Créer l'exporteur JSON
        json_exporter = JsonExporter()

        # Exporter directement les polygones dessinés (exactement ce que l'utilisateur voit)
        result_path = json_exporter.export_polygons_to_json(
            model, label_settings, json_path
        )

        if result_path:
            print(f"   ✅ JSON exporté: {json_filename}")
            print(f"   📄 Fichier JSON: {json_filename}")
            print(f"   🔄 Compatible avec Kili et réutilisable")
        else:
            print(f"   ❌ Erreur lors de l'export JSON")

    except Exception as e:
        print(f"   ❌ Erreur export JSON: {str(e)}")


def save_individual_masks(m1, m2, folder, fname, label_settings):
    """
    Sauvegarde les masques avec paramètres individuels.

    Args:
        m1: Masque standard
        m2: Masque polygonal
        folder: Dossier de destination
        fname: Nom du fichier
        label_settings: Paramètres utilisés pour chaque label
    """
    # Créer les sous-dossiers
    std_dir = os.path.join(folder, "standard")
    poly_dir = os.path.join(folder, "polygon")
    vis_dir = os.path.join(folder, "visual")

    os.makedirs(std_dir, exist_ok=True)
    os.makedirs(poly_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)

    # Sauvegarder les masques
    Image.fromarray(m1).save(os.path.join(std_dir, fname))
    Image.fromarray(m2).save(os.path.join(poly_dir, fname))

    # Créer la version visuelle avec les couleurs RGB pour l'export
    rgb = np.zeros((*m1.shape, 3), dtype=np.uint8)
    for class_value, rgb_color in MASK_COLORS_RGB.items():
        rgb[m1 == class_value] = rgb_color
    Image.fromarray(rgb).save(os.path.join(vis_dir, fname))

    # Afficher les paramètres utilisés
    print(f"✅ INDIVIDUAL masks exported: {fname}")
    for label, settings in label_settings.items():
        pixels = np.sum(m1 == {'frontwall': 1, 'backwall': 2, 'flaw': 3, 'indication': 4}[label])
        print(f"   {label}: threshold={settings['threshold']}, type={settings['mask_type']}, "
              f"smooth={settings['smooth_contours']}, pixels={pixels}")

    total_pixels = m1.size
    frontwall_pixels = np.sum(m1 == 1)
    backwall_pixels = np.sum(m1 == 2)
    flaw_pixels = np.sum(m1 == 3)
    indication_pixels = np.sum(m1 == 4)
    print(f"   Total: Frontwall={frontwall_pixels} | Backwall={backwall_pixels} | Flaw={flaw_pixels} | Indication={indication_pixels} pixels")


def export_annotations_to_json(model, label_settings, output_dir="exports"):
    """
    Exporte les annotations actuelles vers un fichier JSON compatible Kili.

    Args:
        model: Modèle contenant les polygones et images
        label_settings: Paramètres individuels par label
        output_dir: Dossier de sortie

    Returns:
        Chemin du fichier JSON généré
    """
    logger = logging.getLogger(__name__)

    try:
        # Créer l'exporteur JSON
        json_exporter = JsonExporter()

        # Générer le nom de fichier
        json_filename = generate_json_filename("mask_annotations")
        json_path = os.path.join(output_dir, "json", json_filename)

        # Exporter uniquement l'image actuelle
        result_path = json_exporter.export_current_image_to_json(
            model, label_settings, json_path
        )

        print(f"✅ JSON exporté: {os.path.basename(result_path)}")
        print(f"   📁 Dossier: {os.path.dirname(result_path)}")
        print(f"   🔄 Compatible avec Kili et réutilisable")

        return result_path

    except Exception as e:
        logger.error(f"Erreur lors de l'export JSON: {str(e)}")
        print(f"❌ Erreur export JSON: {str(e)}")
        return None


def export_all_annotations_to_json(model, label_settings, output_dir="exports"):
    """
    Exporte toutes les annotations vers un fichier JSON.

    Args:
        model: Modèle contenant les polygones et images
        label_settings: Paramètres individuels par label
        output_dir: Dossier de sortie

    Returns:
        Chemin du fichier JSON généré
    """
    logger = logging.getLogger(__name__)

    try:
        # Créer l'exporteur JSON
        json_exporter = JsonExporter()

        # Générer le nom de fichier
        json_filename = generate_json_filename("all_annotations")
        json_path = os.path.join(output_dir, "json", json_filename)

        # Exporter toutes les images
        result_path = json_exporter.export_annotations_to_json(
            model, label_settings, json_path
        )

        print(f"✅ JSON complet exporté: {os.path.basename(result_path)}")
        print(f"   📁 Dossier: {os.path.dirname(result_path)}")
        print(f"   📊 Toutes les images avec annotations")
        print(f"   🔄 Compatible avec Kili et réutilisable")

        return result_path

    except Exception as e:
        logger.error(f"Erreur lors de l'export JSON complet: {str(e)}")
        print(f"❌ Erreur export JSON complet: {str(e)}")
        return None


def load_annotations_from_json(json_path, model):
    """
    Charge les annotations depuis un fichier JSON.

    Args:
        json_path: Chemin vers le fichier JSON
        model: Modèle pour obtenir les dimensions d'image

    Returns:
        Dictionnaire des polygones chargés
    """
    logger = logging.getLogger(__name__)

    try:
        # Créer l'exporteur JSON
        json_exporter = JsonExporter()

        # Charger les données JSON
        json_data = json_exporter.load_annotations_from_json(json_path)

        # Obtenir les dimensions de l'image actuelle
        current_image_path = model.image_list[model.current_index]
        import cv2
        image = cv2.imread(current_image_path)
        if image is None:
            raise ValueError(f"Impossible de charger l'image: {current_image_path}")

        height, width = image.shape[:2]

        # Passer la marge du modèle à l'exporteur pour l'import
        json_exporter._import_margin = model.margin

        # Convertir en polygones
        polygons = json_exporter.convert_json_to_polygons(
            json_data, width, height, model.current_index
        )

        print(f"✅ Annotations chargées depuis: {os.path.basename(json_path)}")
        for label, polys in polygons.items():
            if polys:
                print(f"   • {label}: {len(polys)} polygone(s)")

        return polygons

    except Exception as e:
        logger.error(f"Erreur lors du chargement JSON: {str(e)}")
        print(f"❌ Erreur chargement JSON: {str(e)}")
        return {"frontwall": [], "backwall": [], "flaw": [], "indication": []}


class MaskExporter:
    def __init__(self, settings):
        self._settings = settings
        self._logger = logging.getLogger(__name__)

    def export_polygonal_masks(self, model, binary_mask, output_folder, fname):
        h, w = binary_mask.shape
        mask_poly = np.zeros((h, w), dtype=np.uint8)
        for label, val in model.class_map.items():
            for pts in model.polygons[label]:
                if len(pts) >= 3:
                    pts_corr = normalize_polygon_coords(pts, model.margin)
                    tmp = np.zeros_like(binary_mask, dtype=np.uint8)
                    cv2.fillPoly(tmp, [np.array(pts_corr, dtype=np.int32)], 1)
                    tmp &= binary_mask
                    mask_poly = np.maximum(mask_poly, generate_profile_mask(tmp, val))
        # Apply smoothing
        smooth_radius = self._settings.get('smooth_kernel', 0)
        if smooth_radius > 0:
            mask_poly = smooth_mask_contours(mask_poly, smooth_radius)
        # Save result
        Image.fromarray(mask_poly).save(os.path.join(output_folder, fname))
