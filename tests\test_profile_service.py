import numpy as np
from services.profile_service import ProfileService


def test_generate_polygon_mask_empty():
    service = ProfileService(smoothing=False)
    mask = service.generate_polygon_mask([], (10, 10))
    assert mask.shape == (10, 10)
    assert np.all(mask == 0)


def test_generate_polygon_mask_single_triangle():
    service = ProfileService(smoothing=False)
    pts = np.array([[1, 1], [5, 1], [3, 4]])
    mask = service.generate_polygon_mask([{'points': pts, 'threshold': 0}], (6, 6))
    assert mask.shape == (6, 6)
    # Le centre du triangle devrait être marqué
    assert mask[2, 3] == 1
    # Vérifier qu'il y a au moins un pixel du polygone
    assert np.sum(mask == 1) > 0