#!/usr/bin/env python3
"""
Script simple pour tester les corrections apportées.
"""
import os
import sys
import numpy as np
import tempfile
from PIL import Image

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.constants import MASK_COLORS_RGB, MASK_COLORS_BGR, CLASS_MAP
from services.export_service import save_unified_masks
from models.mask_model import MaskModel


def test_color_consistency():
    """Test de cohérence des couleurs."""
    print("=== Test de cohérence des couleurs ===")
    
    # Vérifier que les constantes BGR et RGB sont cohérentes
    print("Vérification des constantes de couleurs...")
    for class_value in CLASS_MAP.values():
        if class_value in MASK_COLORS_BGR and class_value in MASK_COLORS_RGB:
            bgr = MASK_COLORS_BGR[class_value]
            rgb = MASK_COLORS_RGB[class_value]
            expected_rgb = [bgr[2], bgr[1], bgr[0]]  # BGR -> RGB
            
            if rgb == expected_rgb:
                print(f"✅ Classe {class_value}: BGR {bgr} -> RGB {rgb} (correct)")
            else:
                print(f"❌ Classe {class_value}: BGR {bgr} -> RGB {rgb}, attendu {expected_rgb}")
                return False
    
    print("✅ Toutes les constantes de couleurs sont cohérentes")
    return True


def test_mask_export():
    """Test d'exportation des masques."""
    print("\n=== Test d'exportation des masques ===")
    
    try:
        # Créer un répertoire temporaire
        temp_dir = tempfile.mkdtemp()
        print(f"Répertoire temporaire: {temp_dir}")
        
        # Créer un masque de test
        test_mask = np.zeros((100, 100), dtype=np.uint8)
        test_mask[10:30, 10:30] = 1  # frontwall
        test_mask[40:60, 10:30] = 2  # backwall
        test_mask[10:30, 40:60] = 3  # flaw
        test_mask[40:60, 40:60] = 4  # indication
        
        print("Masque de test créé avec toutes les classes")
        
        # Paramètres de test
        label_settings = {
            'frontwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'backwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'flaw': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'indication': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False}
        }
        
        # Créer un modèle minimal
        model = MaskModel(class_map=CLASS_MAP)
        
        # Exporter le masque
        print("Exportation du masque...")
        save_unified_masks(test_mask, temp_dir, "test_image.png", label_settings, model)
        
        # Vérifier que les fichiers ont été créés
        binary_path = os.path.join(temp_dir, "masks_binary", "test_image.png")
        visual_path = os.path.join(temp_dir, "masks_visual", "test_image.png")
        
        if os.path.exists(binary_path):
            print("✅ Masque binaire créé")
        else:
            print("❌ Masque binaire non créé")
            return False
            
        if os.path.exists(visual_path):
            print("✅ Masque visuel créé")
        else:
            print("❌ Masque visuel non créé")
            return False
        
        # Vérifier les couleurs du masque visuel
        print("Vérification des couleurs du masque visuel...")
        exported_image = np.array(Image.open(visual_path))
        
        for class_value, expected_rgb in MASK_COLORS_RGB.items():
            class_pixels = (test_mask == class_value)
            if np.any(class_pixels):
                exported_colors = exported_image[class_pixels]
                unique_colors = np.unique(exported_colors.reshape(-1, 3), axis=0)
                
                if len(unique_colors) == 1:
                    actual_color = unique_colors[0].tolist()
                    if actual_color == expected_rgb:
                        print(f"✅ Classe {class_value}: couleur correcte {actual_color}")
                    else:
                        print(f"❌ Classe {class_value}: couleur incorrecte {actual_color}, attendu {expected_rgb}")
                        return False
                else:
                    print(f"❌ Classe {class_value}: couleurs incohérentes")
                    return False
        
        print("✅ Toutes les couleurs du masque visuel sont correctes")
        
        # Nettoyage
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'exportation: {str(e)}")
        return False


def test_mvc_structure():
    """Test de la structure MVC."""
    print("\n=== Test de la structure MVC ===")
    
    try:
        # Test du modèle
        print("Test du modèle...")
        model = MaskModel(class_map=CLASS_MAP)
        
        # Test d'encapsulation
        polygons1 = model.get_all_polygons()
        polygons2 = model.get_all_polygons()
        polygons1['frontwall'].append([(10, 10), (20, 20)])
        
        if len(polygons2['frontwall']) == 0:
            print("✅ Le modèle retourne des copies (encapsulation correcte)")
        else:
            print("❌ Le modèle ne retourne pas de copies")
            return False
        
        # Test d'ajout/suppression de polygones
        test_points = [(10, 10), (20, 20), (30, 30)]
        model.add_polygon('frontwall', test_points)
        
        polygons = model.get_all_polygons()
        if len(polygons['frontwall']) == 1 and polygons['frontwall'][0] == test_points:
            print("✅ Ajout de polygone fonctionne")
        else:
            print("❌ Ajout de polygone ne fonctionne pas")
            return False
        
        # Test de suppression
        result = model.remove_last_polygon('frontwall')
        polygons = model.get_all_polygons()
        
        if result and len(polygons['frontwall']) == 0:
            print("✅ Suppression de polygone fonctionne")
        else:
            print("❌ Suppression de polygone ne fonctionne pas")
            return False
        
        # Test de validation
        try:
            model.add_polygon('invalid_label', [(10, 10)])
            print("❌ Validation des labels ne fonctionne pas")
            return False
        except ValueError:
            print("✅ Validation des labels fonctionne")
        
        print("✅ Structure MVC du modèle correcte")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test MVC: {str(e)}")
        return False


def main():
    """Fonction principale."""
    print("Tests de correction des problèmes identifiés\n")
    
    success = True
    
    # Test 1: Cohérence des couleurs
    if not test_color_consistency():
        success = False
    
    # Test 2: Exportation des masques
    if not test_mask_export():
        success = False
    
    # Test 3: Structure MVC
    if not test_mvc_structure():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("✅ TOUS LES TESTS SONT PASSÉS")
        print("Les corrections ont été appliquées avec succès!")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Des corrections supplémentaires sont nécessaires.")
    
    return success


if __name__ == "__main__":
    main()
