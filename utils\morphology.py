"""
Opérations de morphologie et lissage des contours de masques.
"""
import numpy as np

def smooth_mask_contours(mask: np.ndarray, kernel_size: int = 5) -> np.ndarray:
    """
    Applique un closing (dilatation puis érosion) pour lisser les contours d'un masque.
    Si OpenCV (`cv2`) n'est pas disponible, renvoie simplement le masque original.

    Args:
        mask: Masque binaire HxW
        kernel_size: <PERSON>lle du noyau carré
        
    Returns:
        Masque lissé
    """
    try:
        import cv2
    except ImportError:
        # OpenCV non installé, pas de lissage
        return mask
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    return closed 