#!/usr/bin/env python3
"""
Script pour supprimer toutes les références à _save_current_label_settings
et corriger les erreurs liées aux paramètres par label.
"""
import re


def fix_annotation_view():
    """Corrige le fichier annotation_view.py."""
    file_path = "views/annotation_view.py"
    
    # Lire le fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Supprimer les lignes avec _save_current_label_settings
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if '_save_current_label_settings()' in line:
            # Remplacer par un commentaire
            indent = len(line) - len(line.lstrip())
            new_lines.append(' ' * indent + '# Note: Paramètres maintenant liés aux polygones individuels')
        else:
            new_lines.append(line)
    
    content = '\n'.join(new_lines)
    
    # Corrections spécifiques
    replacements = [
        # Supprimer les références à _label_settings dans vertical_gap
        (r'self\._label_settings\[self\._controller\.get_current_label\(\)\]\[\'vertical_gap\'\]', 
         'self._vertical_gap_var.get()'),
        
        # Corriger les références à MASK_COLORS_ALPHA
        (r'self\.MASK_COLORS_ALPHA', 'MASK_COLORS_BGR'),
        
        # Supprimer les imports inutilisés de MASK_COLORS_BGRA
        (r'from config\.constants import LABEL_COLORS_HEX, MASK_COLORS_BGR, MASK_COLORS_BGRA', 
         'from config.constants import LABEL_COLORS_HEX, MASK_COLORS_BGR'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Écrire le fichier corrigé
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Fichier {file_path} corrigé")


def main():
    """Fonction principale."""
    print("Correction des références aux paramètres par label...")
    fix_annotation_view()
    print("✅ Corrections terminées")


if __name__ == "__main__":
    main()
