#!/usr/bin/env python3
"""
Test pour vérifier que les paramètres sont liés aux polygones individuels.
"""
import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.mask_model import MaskModel
from config.constants import CLASS_MAP, LABEL_SETTINGS


def test_polygon_individual_parameters():
    """Test que les paramètres sont liés aux polygones individuels."""
    print("=== Test des paramètres individuels des polygones ===")
    
    # Créer un modèle
    model = MaskModel(class_map=CLASS_MAP, label_settings=LABEL_SETTINGS)
    
    # Test 1: Ajouter des polygones temporaires avec différents paramètres
    print("\n1. Test des polygones temporaires avec paramètres différents")
    
    # Polygone 1 avec threshold 100
    points1 = [(10, 10), (20, 10), (20, 20), (10, 20), (10, 10)]
    params1 = {
        'threshold': 100,
        'alpha': 30,
        'mask_type': 'standard',
        'smooth_contours': False,
        'drawing_mode': 'polygon',
        'vertical_gap': 5
    }
    model.add_temporary_polygon(points1, params1)
    
    # Polygone 2 avec threshold 200
    points2 = [(30, 30), (40, 30), (40, 40), (30, 40), (30, 30)]
    params2 = {
        'threshold': 200,
        'alpha': 60,
        'mask_type': 'polygon',
        'smooth_contours': True,
        'drawing_mode': 'rectangle',
        'vertical_gap': 10
    }
    model.add_temporary_polygon(points2, params2)
    
    # Vérifier les polygones temporaires
    temp_polygons = model.get_temporary_polygons()
    assert len(temp_polygons) == 2, f"Attendu 2 polygones temporaires, trouvé {len(temp_polygons)}"
    
    assert temp_polygons[0]['parameters']['threshold'] == 100, "Threshold du polygone 1 incorrect"
    assert temp_polygons[1]['parameters']['threshold'] == 200, "Threshold du polygone 2 incorrect"
    
    print("✅ Polygones temporaires avec paramètres différents créés")
    
    # Test 2: Valider les polygones temporaires
    print("\n2. Test de validation des polygones temporaires")
    
    model.commit_temporary_polygons()
    
    # Vérifier que les polygones sont maintenant définitifs
    all_polygons = model.get_all_polygons()
    frontwall_polygons = all_polygons['frontwall']
    
    assert len(frontwall_polygons) == 2, f"Attendu 2 polygones frontwall, trouvé {len(frontwall_polygons)}"
    
    # Vérifier que les paramètres sont conservés
    poly1 = frontwall_polygons[0]
    poly2 = frontwall_polygons[1]
    
    assert poly1['parameters']['threshold'] == 100, "Paramètres du polygone 1 non conservés"
    assert poly2['parameters']['threshold'] == 200, "Paramètres du polygone 2 non conservés"
    
    assert poly1['parameters']['mask_type'] == 'standard', "Type de masque du polygone 1 incorrect"
    assert poly2['parameters']['mask_type'] == 'polygon', "Type de masque du polygone 2 incorrect"
    
    print("✅ Polygones validés avec paramètres conservés")
    
    # Test 3: Ajouter un nouveau polygone avec des paramètres différents
    print("\n3. Test d'ajout d'un nouveau polygone avec paramètres différents")
    
    # Nouveau polygone avec threshold 150
    points3 = [(50, 50), (60, 50), (60, 60), (50, 60), (50, 50)]
    params3 = {
        'threshold': 150,
        'alpha': 80,
        'mask_type': 'standard',
        'smooth_contours': False,
        'drawing_mode': 'polygon',
        'vertical_gap': 3
    }
    model.add_temporary_polygon(points3, params3)
    model.commit_temporary_polygons()
    
    # Vérifier que le nouveau polygone a ses propres paramètres
    all_polygons = model.get_all_polygons()
    frontwall_polygons = all_polygons['frontwall']
    
    assert len(frontwall_polygons) == 3, f"Attendu 3 polygones frontwall, trouvé {len(frontwall_polygons)}"
    
    poly3 = frontwall_polygons[2]
    assert poly3['parameters']['threshold'] == 150, "Paramètres du polygone 3 incorrects"
    
    # Vérifier que les anciens polygones gardent leurs paramètres
    assert frontwall_polygons[0]['parameters']['threshold'] == 100, "Paramètres du polygone 1 modifiés"
    assert frontwall_polygons[1]['parameters']['threshold'] == 200, "Paramètres du polygone 2 modifiés"
    
    print("✅ Nouveau polygone ajouté sans affecter les anciens")
    
    # Test 4: Vérifier la compatibilité avec get_all_polygons_points_only
    print("\n4. Test de compatibilité avec l'ancienne interface")
    
    points_only = model.get_all_polygons_points_only()
    frontwall_points = points_only['frontwall']
    
    assert len(frontwall_points) == 3, f"Attendu 3 listes de points, trouvé {len(frontwall_points)}"
    assert frontwall_points[0] == points1, "Points du polygone 1 incorrects"
    assert frontwall_points[1] == points2, "Points du polygone 2 incorrects"
    assert frontwall_points[2] == points3, "Points du polygone 3 incorrects"
    
    print("✅ Compatibilité avec l'ancienne interface maintenue")
    
    return True


def test_parameter_isolation():
    """Test que les paramètres des polygones sont isolés."""
    print("\n=== Test d'isolation des paramètres ===")
    
    model = MaskModel(class_map=CLASS_MAP, label_settings=LABEL_SETTINGS)
    
    # Créer deux polygones avec des paramètres très différents
    points_a = [(0, 0), (10, 0), (10, 10), (0, 10), (0, 0)]
    params_a = {
        'threshold': 50,
        'alpha': 25,
        'mask_type': 'standard',
        'smooth_contours': False
    }
    
    points_b = [(20, 20), (30, 20), (30, 30), (20, 30), (20, 20)]
    params_b = {
        'threshold': 250,
        'alpha': 75,
        'mask_type': 'polygon',
        'smooth_contours': True
    }
    
    # Ajouter les polygones
    model.add_temporary_polygon(points_a, params_a)
    model.add_temporary_polygon(points_b, params_b)
    model.commit_temporary_polygons()
    
    # Vérifier l'isolation
    all_polygons = model.get_all_polygons()
    frontwall_polygons = all_polygons['frontwall']
    
    poly_a = frontwall_polygons[0]
    poly_b = frontwall_polygons[1]
    
    # Modifier les paramètres du polygone A ne doit pas affecter B
    original_params_b = poly_b['parameters'].copy()
    
    # Les paramètres doivent être différents
    assert poly_a['parameters']['threshold'] != poly_b['parameters']['threshold'], "Paramètres non isolés"
    assert poly_a['parameters']['mask_type'] != poly_b['parameters']['mask_type'], "Types de masque non isolés"
    
    # Vérifier que les paramètres sont bien conservés
    assert poly_a['parameters']['threshold'] == 50, "Threshold du polygone A incorrect"
    assert poly_b['parameters']['threshold'] == 250, "Threshold du polygone B incorrect"
    
    print("✅ Paramètres des polygones correctement isolés")
    
    return True


def main():
    """Fonction principale."""
    print("Tests des paramètres individuels des polygones\n")
    
    success = True
    
    try:
        if not test_polygon_individual_parameters():
            success = False
    except Exception as e:
        print(f"❌ Erreur dans test_polygon_individual_parameters: {e}")
        success = False
    
    try:
        if not test_parameter_isolation():
            success = False
    except Exception as e:
        print(f"❌ Erreur dans test_parameter_isolation: {e}")
        success = False
    
    print("\n" + "="*50)
    if success:
        print("✅ TOUS LES TESTS SONT PASSÉS")
        print("Les paramètres sont correctement liés aux polygones individuels !")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Des corrections sont nécessaires.")
    
    return success


if __name__ == "__main__":
    main()
