"""
Fonctions utilitaires pour la gestion des fichiers et images.
"""
from pathlib import Path
from typing import List

def ensure_dir(path: str) -> None:
    """
    Crée le dossier et ses parents si nécessaire.
    
    Args:
        path: Chemin du dossier à créer
    """
    Path(path).mkdir(parents=True, exist_ok=True)

def list_images(folder_path: str, extensions: List[str] = None) -> List[str]:
    """
    Retourne la liste des fichiers image dans un dossier.

    Args:
        folder_path: Chemin vers le dossier
        extensions: Extensions à filtrer (par défaut ['.png','.jpg','.jpeg'])
        
    Returns:
        Liste de chemins absolus
    """
    if extensions is None:
        extensions = ['.png', '.jpg', '.jpeg']
    folder = Path(folder_path)
    return [str(p) for p in folder.iterdir() if p.suffix.lower() in extensions] 