# === model/mask_model.py ===
import os
import numpy as np
import cv2
import logging
from typing import List, Dict, Tuple


class MaskModel:
    """
    Modèle pour la gestion des masques d'annotation.
    Gère les données des images et des polygones d'annotation.
    """
    
    # Constantes de classe
    DEFAULT_MARGIN = 100
    VALID_LABELS = ['frontwall', 'backwall', 'flaw', 'indication']

    def __init__(self,
                 image_paths: list = None,
                 class_map: dict = None,
                 label_settings: dict = None):
        """
        Initialise le modèle avec des valeurs par défaut.
        
        Args:
            image_paths: Liste des chemins d'images
            class_map: Mapping des labels vers des valeurs de classe
            label_settings: Réglages par défaut pour chaque label
        """
        # Liste des chemins d'images
        self._image_list = image_paths or []
        # Exposer publiquement la liste pour le contrôleur
        self.image_paths = self._image_list
        
        self._current_index = 0
        self._current_image = None
        self._current_label = 'frontwall'
        # Polygones définitifs : chaque polygone a ses propres paramètres
        self._polygons = {label: [] for label in self.VALID_LABELS}  # Liste de dict {points, parameters}
        self._temporary_polygons = []  # Polygones temporaires avec leurs paramètres
        self._labels = self.VALID_LABELS
        self._current_label_index = 0
        self._current_polygon = []
        self._image_original = None
        self._margin = self.DEFAULT_MARGIN
        self._logger = logging.getLogger(__name__)
        
        # Sauvegarde de la map de classes et des réglages
        self.class_map = class_map or {l: i+1 for i, l in enumerate(self.VALID_LABELS)}
        self.label_settings = label_settings or {}

        # seuil par défaut pour chaque label
        self._thresholds: dict[str, int] = {
            label: 127 for label in self.VALID_LABELS
        }

    @property
    def image_list(self) -> list:
        """Retourne la liste des images."""
        return self._image_list

    @image_list.setter
    def image_list(self, value: list):
        """
        Définit la liste des images.
        
        Args:
            value: Liste des chemins d'images
        """
        if not isinstance(value, list):
            raise ValueError("image_list doit être une liste")
        self._image_list = value
        self.image_paths = value  # Mettre à jour aussi l'attribut public
        self._current_index = 0
        self._logger.info(f"Liste d'images mise à jour: {len(value)} images")

    @property
    def current_index(self) -> int:
        """Retourne l'index de l'image courante."""
        return self._current_index

    @current_index.setter
    def current_index(self, value: int):
        """
        Définit l'index de l'image courante.
        
        Args:
            value: Nouvel index
        """
        if not isinstance(value, int):
            raise ValueError("current_index doit être un entier")
        if value < 0 or (self._image_list and value >= len(self._image_list)):
            raise ValueError(f"Index invalide: {value}")
        self._current_index = value
        self._logger.info(f"Index courant mis à jour: {value}")

    @property
    def current_label_index(self) -> int:
        """Retourne l'index du label courant."""
        return self._current_label_index

    @current_label_index.setter
    def current_label_index(self, value: int):
        """
        Définit l'index du label courant.
        
        Args:
            value: Nouvel index
        """
        if not isinstance(value, int):
            raise ValueError("current_label_index doit être un entier")
        if value < 0 or value >= len(self.VALID_LABELS):
            raise ValueError(f"Index de label invalide: {value}")
        # On met à jour l'index **et** la chaîne du label courant
        self._current_label_index = value
        self._current_label = self.VALID_LABELS[value]
        self._logger.info(f"Index de label mis à jour: {value} → label courant = {self._current_label}")

    @property
    def labels(self) -> list:
        """Retourne la liste des labels disponibles."""
        return self.VALID_LABELS

    @property
    def current_label(self) -> str:
        """Retourne le label actuel."""
        return self._current_label

    @property
    def image_original(self) -> np.ndarray:
        """
        Retourne l'image originale.
        
        Returns:
            numpy.ndarray: Image originale en format BGR
        """
        if self._current_image is None:
            self._logger.error("Tentative d'accès à l'image originale avant son chargement")
            return None
        return self._current_image

    @property
    def margin(self) -> int:
        """
        Retourne la marge actuelle.
        
        Returns:
            int: Valeur de la marge en pixels
        """
        return self._margin

    @margin.setter
    def margin(self, value: int):
        """
        Définit la marge.
        
        Args:
            value: Nouvelle valeur de marge en pixels
        """
        if not isinstance(value, int):
            raise ValueError("margin doit être un entier")
        if value < 0:
            raise ValueError("margin doit être positif")
        self._margin = value
        self._logger.info(f"Marge mise à jour: {value}")

    def image_with_margin(self):
        """
        Retourne l'image avec une marge blanche.
        
        Returns:
            numpy.ndarray: Image avec marge
        """
        if self._current_image is None:
            self._logger.error("Tentative d'accès à l'image avant son chargement")
            return None
            
        return cv2.copyMakeBorder(
            self._current_image,
            self._margin,
            self._margin,
            self._margin,
            self._margin,
            cv2.BORDER_CONSTANT,
            value=(255, 255, 255),
        )

    def load_current_image(self):
        """
        Charge l'image courante depuis la liste des images.
        Réinitialise les polygones et le label courant.
        
        Raises:
            ValueError: Si l'image ne peut pas être chargée
        """
        if not self._image_list:
            self._logger.warning("Tentative de chargement d'image avec une liste vide")
            return
            
        path = os.path.abspath(self._image_list[self._current_index])
        self._logger.info(f"Chargement de l'image: {path}")
        
        # Vérifier si le fichier existe
        if not os.path.exists(path):
            error_msg = f"Le fichier n'existe pas: {path}"
            self._logger.error(error_msg)
            raise ValueError(error_msg)
            
        # Charger l'image
        img = cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            error_msg = f"Impossible de charger l'image: {path}"
            self._logger.error(error_msg)
            raise ValueError(error_msg)
            
        # Vérifier les dimensions de l'image
        if img.size == 0:
            error_msg = f"L'image est vide: {path}"
            self._logger.error(error_msg)
            raise ValueError(error_msg)
            
        # Vérifier le format de l'image
        if len(img.shape) != 3 or img.shape[2] != 3:
            error_msg = f"Format d'image invalide (attendu: BGR, reçu: {img.shape}): {path}"
            self._logger.error(error_msg)
            raise ValueError(error_msg)
            
        self._logger.info(f"Image chargée avec succès: {img.shape}")
        self._current_image = img.copy()
        self._polygons = {label: [] for label in self.VALID_LABELS}
        self._current_label_index = 0

    def current_filename(self):
        """
        Retourne le nom du fichier de l'image courante.
        
        Returns:
            str: Nom du fichier
        """
        if not self._image_list:
            return ""
        return os.path.basename(self._image_list[self._current_index])

    @property
    def polygons(self):
        """Retourne le dictionnaire des polygones."""
        return self._polygons

    @polygons.setter
    def polygons(self, value):
        """Définit le dictionnaire des polygones."""
        if not isinstance(value, dict):
            raise ValueError("polygons doit être un dictionnaire")
        self._polygons = value
        self._logger.info(f"Polygones mis à jour: {len(value)} labels")

    def get_all_polygons(self):
        """
        Retourne tous les polygones annotés avec leurs paramètres.

        Returns:
            Dict[str, List[Dict]]: Dictionnaire des polygones par label
            Chaque polygone est un dict avec 'points' et 'parameters'
        """
        return {label: polygons.copy() for label, polygons in self._polygons.items()}

    def get_all_polygons_points_only(self):
        """
        Retourne seulement les points des polygones (pour compatibilité).

        Returns:
            Dict[str, List[List[Tuple[int, int]]]]: Dictionnaire des points par label
        """
        result = {}
        for label, polygons in self._polygons.items():
            result[label] = [poly['points'] for poly in polygons]
        return result

    def add_polygon(self, label: str, points: List[Tuple[int, int]], parameters: Dict = None) -> None:
        """
        Ajoute un polygone avec ses paramètres pour un label donné.

        Args:
            label: Label du polygone
            points: Liste des points du polygone
            parameters: Paramètres utilisés pour créer ce polygone
        """
        if label not in self.VALID_LABELS:
            raise ValueError(f"Label invalide: {label}")
        if not points:
            return

        polygon_data = {
            'points': points.copy(),
            'parameters': parameters.copy() if parameters else {}
        }
        self._polygons[label].append(polygon_data)
        self._logger.info(f"Polygone avec paramètres ajouté pour le label: {label}")

    def remove_last_polygon(self, label: str) -> bool:
        """
        Supprime le dernier polygone d'un label donné.

        Args:
            label: Label du polygone à supprimer

        Returns:
            True si un polygone a été supprimé, False sinon
        """
        if label not in self.VALID_LABELS:
            raise ValueError(f"Label invalide: {label}")
        if self._polygons[label]:
            self._polygons[label].pop()
            self._logger.info(f"Dernier polygone supprimé pour le label: {label}")
            return True
        return False

    def set_polygons(self, polygons: Dict[str, List[List[Tuple[int, int]]]]) -> None:
        """
        Remplace tous les polygones par de nouveaux.

        Args:
            polygons: Nouveau dictionnaire de polygones
        """
        # Valider la structure
        for label in polygons.keys():
            if label not in self.VALID_LABELS:
                raise ValueError(f"Label invalide: {label}")

        # Réinitialiser et copier
        self._polygons = {label: [] for label in self.VALID_LABELS}
        for label, label_polygons in polygons.items():
            self._polygons[label] = label_polygons.copy()

        self._logger.info("Polygones remplacés")

    def add_temporary_polygon(self, points: List[Tuple[int, int]], parameters: Dict) -> None:
        """
        Ajoute un polygone temporaire avec ses paramètres.

        Args:
            points: Points du polygone
            parameters: Paramètres utilisés pour créer ce polygone
        """
        if not points:
            return

        temp_polygon = {
            'points': points.copy(),
            'parameters': parameters.copy(),
            'label': self._current_label  # Label au moment de la création
        }
        self._temporary_polygons.append(temp_polygon)
        self._logger.info(f"Polygone temporaire ajouté avec paramètres: {parameters}")

    def get_temporary_polygons(self) -> List[Dict]:
        """Retourne la liste des polygones temporaires."""
        return self._temporary_polygons.copy()

    def clear_temporary_polygons(self) -> None:
        """Supprime tous les polygones temporaires."""
        self._temporary_polygons.clear()
        self._logger.info("Polygones temporaires supprimés")

    def commit_temporary_polygons(self) -> None:
        """
        Valide les polygones temporaires et les ajoute aux polygones définitifs.
        Chaque polygone garde ses paramètres individuels.
        """
        for temp_polygon in self._temporary_polygons:
            label = temp_polygon['label']
            points = temp_polygon['points']
            parameters = temp_polygon['parameters']

            # Ajouter le polygone avec ses paramètres figés
            polygon_data = {
                'points': points.copy(),
                'parameters': parameters.copy()
            }
            self._polygons[label].append(polygon_data)
            self._logger.info(f"Polygone temporaire validé pour le label: {label} avec paramètres figés")

        self.clear_temporary_polygons()

    @property
    def threshold(self) -> int:
        """Seuil courant pour le label actif."""
        return self._thresholds[self.current_label]

    @threshold.setter
    def threshold(self, value: int):
        if not 0 <= value <= 255:
            raise ValueError("Threshold doit être entre 0 et 255")
        # n'affecte que le seuil du label actif
        self._thresholds[self.current_label] = value
        self._logger.info(
            f"Threshold pour '{self.current_label}' mis à jour -> {value}"
        )
