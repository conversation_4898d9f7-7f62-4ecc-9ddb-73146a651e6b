#!/usr/bin/env python3
"""
Test rapide pour vérifier la correction des masques polygonaux.
"""
import numpy as np
import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.profile_service import generate_profile_mask
from config.constants import CLASS_MAP


def test_generate_profile_mask_simple():
    """Test simple de la fonction generate_profile_mask."""
    print("=== Test simple de generate_profile_mask ===")
    
    # Créer un masque binaire simple
    test_mask = np.zeros((20, 20), dtype=np.uint8)
    test_mask[5:10, 8:12] = 1  # Rectangle binaire
    
    print("Masque binaire de test:")
    print("Valeurs uniques:", np.unique(test_mask))
    print("Forme:", test_mask.shape)
    print("Pixels non-nuls:", np.sum(test_mask > 0))
    
    # Tester pour chaque classe
    for label, class_value in CLASS_MAP.items():
        print(f"\n--- Test pour {label} (valeur {class_value}) ---")
        
        # Générer le masque de profil
        profile_mask = generate_profile_mask(test_mask, class_value)
        
        # Vérifier les résultats
        unique_values = np.unique(profile_mask)
        print(f"Valeurs uniques dans le résultat: {unique_values}")
        
        # Vérifier que la valeur de classe est présente
        if class_value in unique_values:
            print(f"✅ Valeur {class_value} présente")
            
            # Compter les pixels
            class_pixels = np.sum(profile_mask == class_value)
            original_pixels = np.sum(test_mask > 0)
            print(f"Pixels originaux: {original_pixels}")
            print(f"Pixels de profil: {class_pixels}")
            
            if class_pixels >= original_pixels:
                print(f"✅ Le profil contient au moins autant de pixels que l'original")
            else:
                print(f"❌ Le profil contient moins de pixels que l'original")
                return False
        else:
            print(f"❌ Valeur {class_value} absente du résultat")
            return False
    
    print("\n✅ Test simple réussi!")
    return True


def test_profile_mask_colors():
    """Test que les valeurs de couleurs sont correctes."""
    print("\n=== Test des valeurs de couleurs ===")
    
    # Créer un masque binaire
    test_mask = np.zeros((10, 10), dtype=np.uint8)
    test_mask[3:7, 3:7] = 1
    
    # Tester chaque classe
    results = {}
    for label, class_value in CLASS_MAP.items():
        profile_mask = generate_profile_mask(test_mask, class_value)
        
        # Vérifier que seules les valeurs 0 et class_value sont présentes
        unique_values = set(np.unique(profile_mask))
        expected_values = {0, class_value}
        
        if unique_values == expected_values:
            print(f"✅ {label}: valeurs correctes {unique_values}")
            results[label] = True
        else:
            print(f"❌ {label}: valeurs incorrectes {unique_values}, attendu {expected_values}")
            results[label] = False
    
    success = all(results.values())
    if success:
        print("✅ Toutes les valeurs de couleurs sont correctes!")
    else:
        print("❌ Certaines valeurs de couleurs sont incorrectes")
    
    return success


def test_profile_vertical_fill():
    """Test que le profil vertical fonctionne correctement."""
    print("\n=== Test du remplissage vertical ===")
    
    # Créer un masque avec des trous verticaux
    test_mask = np.zeros((10, 10), dtype=np.uint8)
    test_mask[2, 5] = 1  # Pixel en haut
    test_mask[8, 5] = 1  # Pixel en bas
    # Trou au milieu
    
    print("Masque avec trous verticaux:")
    for i in range(10):
        row = ""
        for j in range(10):
            row += "1" if test_mask[i, j] > 0 else "."
        print(f"  {row}")
    
    # Générer le profil
    profile_mask = generate_profile_mask(test_mask, 1)
    
    print("\nMasque de profil résultant:")
    for i in range(10):
        row = ""
        for j in range(10):
            row += "1" if profile_mask[i, j] > 0 else "."
        print(f"  {row}")
    
    # Vérifier que la colonne 5 est remplie de 2 à 8
    col_5 = profile_mask[:, 5]
    expected_fill = np.zeros(10)
    expected_fill[2:9] = 1  # De 2 à 8 inclus
    
    if np.array_equal(col_5, expected_fill):
        print("✅ Remplissage vertical correct!")
        return True
    else:
        print("❌ Remplissage vertical incorrect")
        print(f"Attendu: {expected_fill}")
        print(f"Obtenu:  {col_5}")
        return False


def main():
    """Fonction principale."""
    print("Test rapide de la correction des masques polygonaux\n")
    
    success = True
    
    # Test 1: Fonction de base
    if not test_generate_profile_mask_simple():
        success = False
    
    # Test 2: Valeurs de couleurs
    if not test_profile_mask_colors():
        success = False
    
    # Test 3: Remplissage vertical
    if not test_profile_vertical_fill():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("✅ TOUS LES TESTS RAPIDES SONT PASSÉS")
        print("La correction de generate_profile_mask fonctionne!")
    else:
        print("❌ CERTAINS TESTS RAPIDES ONT ÉCHOUÉ")
        print("La correction nécessite des ajustements.")
    
    return success


if __name__ == "__main__":
    main()
