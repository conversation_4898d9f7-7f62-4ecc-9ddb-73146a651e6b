#!/usr/bin/env python3
"""
Debug direct de la fonction generate_profile_mask.
"""
import numpy as np

def generate_profile_mask_corrected(single_mask: np.ndarray, val: int) -> np.ndarray:
    """
    Version corrigée de generate_profile_mask.
    """
    h, w = single_mask.shape
    profile_mask = np.zeros((h, w), dtype=np.uint8)
    
    # Boolean mask - chercher les pixels non-nuls (valeur 1) dans le masque binaire
    mask_bool = single_mask > 0
    
    # Any column with at least one pixel
    any_col = mask_bool.any(axis=0)
    
    # Compute y_min for each column
    y_min = np.where(any_col, mask_bool.argmax(axis=0), -1)
    
    # Compute y_max by argmax on reversed rows
    y_max = np.where(any_col,
                     h - 1 - mask_bool[::-1].argmax(axis=0),
                     -1)
    
    # Fill between y_min and y_max with the correct class value
    cols = np.nonzero(any_col)[0]
    for x in cols:
        profile_mask[y_min[x]:y_max[x]+1, x] = val
    
    return profile_mask


def test_direct():
    """Test direct de la fonction."""
    print("=== Test direct de la fonction corrigée ===")
    
    # Test simple
    test_mask = np.zeros((10, 10), dtype=np.uint8)
    test_mask[3:6, 4:7] = 1  # Rectangle binaire
    
    print("Masque d'entrée:")
    print(test_mask)
    print(f"Valeurs uniques: {np.unique(test_mask)}")
    
    # Test pour différentes valeurs de classe
    for val in [1, 2, 3, 4]:
        print(f"\n--- Test avec val={val} ---")
        result = generate_profile_mask_corrected(test_mask, val)
        
        print("Résultat:")
        print(result)
        print(f"Valeurs uniques: {np.unique(result)}")
        
        # Vérifier que val est présent
        if val in np.unique(result):
            print(f"✅ Valeur {val} présente")
        else:
            print(f"❌ Valeur {val} absente")
    
    print("\n=== Test avec masque vertical ===")
    
    # Test avec trous verticaux
    test_mask2 = np.zeros((10, 10), dtype=np.uint8)
    test_mask2[2, 5] = 1  # Haut
    test_mask2[8, 5] = 1  # Bas
    
    print("Masque avec trous:")
    print(test_mask2)
    
    result2 = generate_profile_mask_corrected(test_mask2, 2)
    print("Résultat avec profil:")
    print(result2)
    
    # Vérifier le remplissage vertical
    col_5 = result2[:, 5]
    print(f"Colonne 5: {col_5}")
    
    # Doit être rempli de 2 à 8
    expected = np.zeros(10)
    expected[2:9] = 2
    
    if np.array_equal(col_5, expected):
        print("✅ Remplissage vertical correct")
    else:
        print("❌ Remplissage vertical incorrect")
        print(f"Attendu: {expected}")


if __name__ == "__main__":
    test_direct()
