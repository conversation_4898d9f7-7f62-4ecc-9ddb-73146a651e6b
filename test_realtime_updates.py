#!/usr/bin/env python3
"""
Test pour vérifier que les mises à jour en temps réel fonctionnent.
"""
import sys
import os
import time
from unittest.mock import Mock, patch

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_schedule_mask_update_immediate():
    """Test que _schedule_mask_update avec immediate=True fonctionne."""
    print("=== Test de _schedule_mask_update avec immediate=True ===")
    
    # Mock de la classe AnnotationView
    class MockAnnotationView:
        def __init__(self):
            self._update_pending = False
            self._update_mask_display_called = False
            self._root = Mock()
        
        def _update_mask_display(self):
            """Mock de la mise à jour du masque."""
            self._update_mask_display_called = True
            print("✅ _update_mask_display appelée")
        
        def _schedule_mask_update(self, immediate=False):
            """Version corrigée de _schedule_mask_update."""
            if immediate:
                # Mise à jour immédiate pour les interactions en temps réel
                self._update_mask_display()
            else:
                # Mise à jour avec délai pour les opérations lourdes
                if not self._update_pending:
                    self._update_pending = True
                    # Programmer la mise à jour après 50ms (réduit de 100ms)
                    self._root.after(50, self._delayed_mask_update)
        
        def _delayed_mask_update(self):
            """Effectue la mise à jour du masque après le délai."""
            self._update_pending = False
            self._update_mask_display()
    
    # Test avec immediate=True
    view = MockAnnotationView()
    view._schedule_mask_update(immediate=True)
    
    if view._update_mask_display_called:
        print("✅ Mise à jour immédiate fonctionne")
        return True
    else:
        print("❌ Mise à jour immédiate ne fonctionne pas")
        return False


def test_callback_functions():
    """Test que les callbacks utilisent la mise à jour immédiate."""
    print("\n=== Test des callbacks avec mise à jour immédiate ===")
    
    # Mock de la classe AnnotationView avec les callbacks
    class MockAnnotationViewWithCallbacks:
        def __init__(self):
            self._threshold_var = Mock()
            self._alpha_var = Mock()
            self._mask_type_var = Mock()
            self._smooth_contours_var = Mock()
            self._immediate_updates = []
            self._logger = Mock()
        
        def _save_current_label_settings(self):
            """Mock de sauvegarde des paramètres."""
            pass
        
        def _schedule_mask_update(self, immediate=False):
            """Mock qui enregistre si immediate=True est utilisé."""
            if immediate:
                self._immediate_updates.append(True)
                print(f"✅ Mise à jour immédiate demandée (total: {len(self._immediate_updates)})")
            else:
                print("❌ Mise à jour avec délai utilisée")
        
        def _on_threshold_change(self, value):
            """Version corrigée du callback threshold."""
            try:
                self._threshold_var.set(int(value))
                self._save_current_label_settings()
                # Mise à jour immédiate pour le temps réel
                self._schedule_mask_update(immediate=True)
                self._logger.debug(f"Threshold changé: {value}")
            except Exception as e:
                self._logger.error(f"Erreur: {str(e)}")
        
        def _on_alpha_change(self, value):
            """Version corrigée du callback alpha."""
            try:
                self._alpha_var.set(float(value))
                self._save_current_label_settings()
                # Mise à jour immédiate pour le temps réel
                self._schedule_mask_update(immediate=True)
                self._logger.debug(f"Alpha changé: {value}")
            except Exception as e:
                self._logger.error(f"Erreur: {str(e)}")
        
        def _on_mask_type_change(self):
            """Version corrigée du callback type de masque."""
            try:
                mask_type = self._mask_type_var.get()
                self._save_current_label_settings()
                # Mise à jour immédiate
                self._schedule_mask_update(immediate=True)
                self._logger.debug(f"Type de masque changé: {mask_type}")
            except Exception as e:
                self._logger.error(f"Erreur: {str(e)}")
    
    # Test des callbacks
    view = MockAnnotationViewWithCallbacks()
    
    print("Test du callback threshold:")
    view._on_threshold_change(150)
    
    print("Test du callback alpha:")
    view._on_alpha_change(75)
    
    print("Test du callback type de masque:")
    view._on_mask_type_change()
    
    # Vérifier que tous les callbacks ont utilisé immediate=True
    expected_immediate_calls = 3
    actual_immediate_calls = len(view._immediate_updates)
    
    if actual_immediate_calls == expected_immediate_calls:
        print(f"✅ Tous les callbacks utilisent la mise à jour immédiate ({actual_immediate_calls}/{expected_immediate_calls})")
        return True
    else:
        print(f"❌ Certains callbacks n'utilisent pas la mise à jour immédiate ({actual_immediate_calls}/{expected_immediate_calls})")
        return False


def test_realtime_responsiveness():
    """Test de la réactivité en temps réel."""
    print("\n=== Test de réactivité en temps réel ===")
    
    class MockRealtimeView:
        def __init__(self):
            self.update_times = []
            self.start_time = time.time()
        
        def _update_mask_display(self):
            """Enregistre le temps de mise à jour."""
            current_time = time.time()
            elapsed = (current_time - self.start_time) * 1000  # en ms
            self.update_times.append(elapsed)
            print(f"Mise à jour à {elapsed:.1f}ms")
        
        def _schedule_mask_update(self, immediate=False):
            """Simule la mise à jour."""
            if immediate:
                self._update_mask_display()
            else:
                # Simuler un délai de 50ms
                time.sleep(0.05)
                self._update_mask_display()
        
        def simulate_slider_movement(self, steps=5):
            """Simule le mouvement d'un slider."""
            print(f"Simulation de {steps} changements de slider:")
            for i in range(steps):
                print(f"  Changement {i+1}:")
                self._schedule_mask_update(immediate=True)
                time.sleep(0.01)  # Petit délai entre les changements
    
    # Test de simulation
    view = MockRealtimeView()
    view.simulate_slider_movement(3)
    
    # Vérifier que les mises à jour sont rapides
    if len(view.update_times) == 3:
        max_delay = max(view.update_times)
        if max_delay < 100:  # Moins de 100ms
            print(f"✅ Mises à jour rapides (max: {max_delay:.1f}ms)")
            return True
        else:
            print(f"❌ Mises à jour trop lentes (max: {max_delay:.1f}ms)")
            return False
    else:
        print(f"❌ Nombre incorrect de mises à jour: {len(view.update_times)}")
        return False


def test_ui_slider_configuration():
    """Test de la configuration des sliders pour le temps réel."""
    print("\n=== Test de configuration des sliders ===")
    
    # Simuler la configuration d'un slider
    slider_config = {
        'threshold_slider': {
            'from_': 0,
            'to': 255,
            'orient': 'horizontal',
            'length': 200,
            'command': '_on_threshold_change'  # Callback en temps réel
        },
        'alpha_slider': {
            'from_': 0,
            'to': 100,
            'orient': 'horizontal',
            'length': 200,
            'command': '_on_alpha_change'  # Callback en temps réel
        }
    }
    
    print("Configuration des sliders:")
    for slider_name, config in slider_config.items():
        print(f"  {slider_name}:")
        print(f"    Plage: {config['from_']} - {config['to']}")
        print(f"    Callback: {config['command']}")
        
        # Vérifier que le callback est configuré
        if 'command' in config and config['command']:
            print(f"    ✅ Callback configuré pour mise à jour temps réel")
        else:
            print(f"    ❌ Pas de callback configuré")
            return False
    
    print("✅ Tous les sliders sont configurés pour le temps réel")
    return True


def main():
    """Fonction principale."""
    print("Tests de mise à jour en temps réel\n")
    
    success = True
    
    # Test 1: Fonction de base
    if not test_schedule_mask_update_immediate():
        success = False
    
    # Test 2: Callbacks
    if not test_callback_functions():
        success = False
    
    # Test 3: Réactivité
    if not test_realtime_responsiveness():
        success = False
    
    # Test 4: Configuration UI
    if not test_ui_slider_configuration():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("✅ TOUS LES TESTS DE TEMPS RÉEL SONT PASSÉS")
        print("Les mises à jour en temps réel fonctionnent correctement!")
    else:
        print("❌ CERTAINS TESTS DE TEMPS RÉEL ONT ÉCHOUÉ")
        print("Des corrections supplémentaires sont nécessaires.")
    
    return success


if __name__ == "__main__":
    main()
