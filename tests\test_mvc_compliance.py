"""
Tests pour vérifier la conformité au pattern MVC.
"""
import unittest
from unittest.mock import Mock, patch
import tempfile
import os

from models.mask_model import MaskModel
from controllers.annotation_controller import AnnotationController
from config.constants import CLASS_MAP, LABEL_SETTINGS


class TestMVCCompliance(unittest.TestCase):
    """Tests de conformité MVC."""
    
    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Créer un modèle de test
        self.model = MaskModel(class_map=CLASS_MAP, label_settings=LABEL_SETTINGS)
        
        # Créer des images de test
        self.test_images = []
        for i in range(3):
            img_path = os.path.join(self.temp_dir, f"test_image_{i}.png")
            # Créer une image simple pour les tests
            import cv2
            import numpy as np
            test_img = np.ones((100, 100, 3), dtype=np.uint8) * 128
            cv2.imwrite(img_path, test_img)
            self.test_images.append(img_path)
    
    def tearDown(self):
        """Nettoyage après les tests."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_model_encapsulation(self):
        """Test que le modèle encapsule correctement ses données."""
        # Test que get_all_polygons retourne une copie
        polygons1 = self.model.get_all_polygons()
        polygons2 = self.model.get_all_polygons()
        
        # Modifier une copie ne doit pas affecter l'autre
        polygons1['frontwall'].append([(10, 10), (20, 20), (30, 30)])
        self.assertEqual(len(polygons2['frontwall']), 0, 
                        "get_all_polygons ne retourne pas une copie")
    
    def test_model_polygon_management(self):
        """Test que le modèle gère correctement les polygones."""
        # Test d'ajout de polygone
        test_points = [(10, 10), (20, 20), (30, 30)]
        self.model.add_polygon('frontwall', test_points)
        
        polygons = self.model.get_all_polygons()
        self.assertEqual(len(polygons['frontwall']), 1)
        self.assertEqual(polygons['frontwall'][0], test_points)
        
        # Test de suppression de polygone
        result = self.model.remove_last_polygon('frontwall')
        self.assertTrue(result)
        
        polygons = self.model.get_all_polygons()
        self.assertEqual(len(polygons['frontwall']), 0)
        
        # Test de suppression sur liste vide
        result = self.model.remove_last_polygon('frontwall')
        self.assertFalse(result)
    
    def test_model_validation(self):
        """Test que le modèle valide correctement les entrées."""
        # Test avec label invalide
        with self.assertRaises(ValueError):
            self.model.add_polygon('invalid_label', [(10, 10)])
        
        with self.assertRaises(ValueError):
            self.model.remove_last_polygon('invalid_label')
        
        # Test avec polygones invalides
        with self.assertRaises(ValueError):
            self.model.set_polygons({'invalid_label': []})
    
    def test_controller_model_interaction(self):
        """Test que le contrôleur interagit correctement avec le modèle."""
        # Mock de la vue pour éviter l'interface graphique
        with patch('views.annotation_view.AnnotationView'):
            with patch('tkinter.Tk'):
                controller = AnnotationController(self.test_images)
                
                # Test d'ajout de polygone via le contrôleur
                test_points = [(10, 10), (20, 20), (30, 30)]
                controller.add_polygon(test_points)
                
                # Vérifier que le polygone a été ajouté au modèle
                polygons = controller.get_all_polygons()
                current_label = controller.get_current_label()
                self.assertEqual(len(polygons[current_label]), 1)
                
                # Test de suppression via le contrôleur
                controller.undo_polygon()
                polygons = controller.get_all_polygons()
                self.assertEqual(len(polygons[current_label]), 0)
    
    def test_controller_does_not_expose_model_internals(self):
        """Test que le contrôleur n'expose pas les détails internes du modèle."""
        with patch('views.annotation_view.AnnotationView'):
            with patch('tkinter.Tk'):
                controller = AnnotationController(self.test_images)
                
                # Le contrôleur ne doit pas exposer directement le modèle
                self.assertFalse(hasattr(controller, 'model'), 
                               "Le contrôleur ne doit pas exposer le modèle directement")
                
                # Le contrôleur doit fournir des méthodes pour accéder aux données
                self.assertTrue(hasattr(controller, 'get_all_polygons'))
                self.assertTrue(hasattr(controller, 'get_current_label'))
                self.assertTrue(hasattr(controller, 'add_polygon'))
                self.assertTrue(hasattr(controller, 'undo_polygon'))
    
    def test_model_state_consistency(self):
        """Test que l'état du modèle reste cohérent."""
        # Ajouter des polygones pour différents labels
        self.model.add_polygon('frontwall', [(10, 10), (20, 20)])
        self.model.add_polygon('backwall', [(30, 30), (40, 40)])
        
        # Changer de label et vérifier que les polygones sont préservés
        original_polygons = self.model.get_all_polygons()
        
        # Simuler un changement de label
        self.model.current_label_index = 1  # backwall
        
        # Les polygones doivent être préservés
        current_polygons = self.model.get_all_polygons()
        self.assertEqual(original_polygons, current_polygons)
    
    def test_model_image_management(self):
        """Test que le modèle gère correctement les images."""
        # Test de chargement de liste d'images
        self.model.image_list = self.test_images
        self.assertEqual(len(self.model.image_list), 3)
        self.assertEqual(self.model.current_index, 0)
        
        # Test de navigation
        self.model.current_index = 1
        self.assertEqual(self.model.current_index, 1)
        
        # Test de validation des index
        with self.assertRaises(ValueError):
            self.model.current_index = -1
        
        with self.assertRaises(ValueError):
            self.model.current_index = 10  # Index trop grand


if __name__ == '__main__':
    unittest.main()
