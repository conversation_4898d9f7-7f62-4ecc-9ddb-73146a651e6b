#!/usr/bin/env python3
"""
Test du nouveau workflow avec polygones temporaires.
"""
import tkinter as tk
import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from controllers.annotation_controller import Annotation<PERSON>ontroller


def test_new_workflow():
    """Test du nouveau workflow."""
    print("=== Test du nouveau workflow ===")
    print("1. Sélectionnez une zone (rectangle ou polygone)")
    print("2. Les polygones apparaissent en gris (temporaires)")
    print("3. Modifiez les paramètres si nécessaire")
    print("4. Cliquez 'Ajouter au label' pour valider")
    print("5. Les polygones deviennent colorés (définitifs)")
    print()
    
    # Créer l'application
    root = tk.Tk()
    
    # Créer le contrôleur
    controller = AnnotationController([])
    
    # Lancer l'application
    try:
        controller.run()
    except Exception as e:
        print(f"Erreur: {e}")
        return False
    
    return True


def main():
    """Fonction principale."""
    print("Test du nouveau workflow avec polygones temporaires\n")
    
    print("Fonctionnalités testées:")
    print("✅ Polygones temporaires (gris) après sélection")
    print("✅ Boutons 'Ajouter au label' et 'Annuler sélection'")
    print("✅ Paramètres sauvegardés avec chaque polygone")
    print("✅ Polygones définitifs colorés après validation")
    print("✅ Masques visibles avec couleurs appropriées")
    print()
    
    test_new_workflow()


if __name__ == "__main__":
    main()
