import numpy as np
from PIL import Image
import json
from pathlib import Path
from services.export_service import ExportService


def test_export_mask_image(tmp_path):
    service = ExportService(output_dir=str(tmp_path))
    # Créer un petit masque 5x5
    mask = np.zeros((5, 5), dtype=np.uint8)
    mask[2, 2] = 1
    out_path = service.export_mask_image(mask, "test")
    # Charger avec Pillow
    img = Image.open(out_path)
    arr = np.array(img)
    assert arr.shape == (5, 5)
    assert arr[2, 2] == 1


def test_export_annotations_json(tmp_path):
    service = ExportService(output_dir=str(tmp_path))
    annotations = {"test": [{'points': [[0,0],[1,0],[1,1]], 'threshold': 0}]}
    out_json = service.export_annotations_json(annotations, "test")
    data = json.loads(Path(out_json).read_text())
    assert "test" in data
    assert isinstance(data["test"], list)