#!/usr/bin/env python3
"""
Test spécifique pour vérifier les couleurs des masques polygonaux.
"""
import os
import sys
import numpy as np
import tempfile
from PIL import Image
import cv2

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.constants import MASK_COLORS_RGB, CLASS_MAP
from services.export_service import export_masks, save_all_masks
from services.profile_service import generate_profile_mask
from models.mask_model import MaskModel


def test_profile_mask_generation():
    """Test que generate_profile_mask fonctionne correctement pour toutes les classes."""
    print("=== Test de génération des masques de profil ===")
    
    # Créer un masque binaire simple
    test_mask = np.zeros((50, 50), dtype=np.uint8)
    test_mask[10:20, 15:25] = 1  # Rectangle binaire
    
    print("Masque binaire de test créé")
    
    # Tester pour chaque classe
    for label, class_value in CLASS_MAP.items():
        print(f"\nTest pour {label} (valeur {class_value}):")
        
        # Générer le masque de profil
        profile_mask = generate_profile_mask(test_mask, class_value)
        
        # Vérifier que le masque contient la bonne valeur
        unique_values = np.unique(profile_mask)
        print(f"  Valeurs uniques dans le masque: {unique_values}")
        
        if class_value in unique_values:
            print(f"  ✅ Valeur {class_value} présente dans le masque")
            
            # Vérifier que la forme est correcte (profil vertical)
            class_pixels = (profile_mask == class_value)
            if np.any(class_pixels):
                # Pour chaque colonne, vérifier qu'il y a un profil continu
                for x in range(15, 25):  # Colonnes où on attend des pixels
                    col_pixels = class_pixels[:, x]
                    if np.any(col_pixels):
                        y_indices = np.where(col_pixels)[0]
                        if len(y_indices) > 0:
                            # Vérifier que c'est continu
                            y_min, y_max = y_indices.min(), y_indices.max()
                            expected_length = y_max - y_min + 1
                            actual_length = len(y_indices)
                            if expected_length == actual_length:
                                print(f"  ✅ Profil vertical continu en colonne {x}")
                            else:
                                print(f"  ❌ Profil discontinu en colonne {x}")
                                return False
        else:
            print(f"  ❌ Valeur {class_value} absente du masque")
            return False
    
    print("✅ Tous les tests de génération de profil sont passés")
    return True


def test_polygon_mask_export():
    """Test que l'export des masques polygonaux produit les bonnes couleurs."""
    print("\n=== Test d'export des masques polygonaux ===")
    
    try:
        # Créer un répertoire temporaire
        temp_dir = tempfile.mkdtemp()
        print(f"Répertoire temporaire: {temp_dir}")
        
        # Créer une image de test
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 128
        image_path = os.path.join(temp_dir, "test_image.png")
        cv2.imwrite(image_path, test_image)
        
        # Créer un modèle avec des polygones de test
        model = MaskModel(image_paths=[image_path], class_map=CLASS_MAP)
        model.load_current_image()
        
        # Ajouter des polygones pour chaque classe
        polygons = {
            'frontwall': [[(10, 10), (30, 10), (30, 30), (10, 30)]],
            'backwall': [[(40, 10), (60, 10), (60, 30), (40, 30)]],
            'flaw': [[(10, 40), (30, 40), (30, 60), (10, 60)]],
            'indication': [[(40, 40), (60, 40), (60, 60), (40, 60)]]
        }
        
        for label, label_polygons in polygons.items():
            for polygon in label_polygons:
                model.add_polygon(label, polygon)
        
        print("Polygones de test ajoutés au modèle")
        
        # Exporter avec type "polygon"
        print("Export des masques polygonaux...")
        export_masks(model, threshold=100, mask_type="polygon", smooth_enabled=False, smooth_radius=0)
        
        # Vérifier que les fichiers ont été créés
        base_dir = os.path.dirname(image_path)
        binary_path = os.path.join(base_dir, "masks_binary", "test_image.png")
        visual_path = os.path.join(base_dir, "masks_visual", "test_image.png")
        
        if not os.path.exists(binary_path):
            print("❌ Masque binaire non créé")
            return False
        
        if not os.path.exists(visual_path):
            print("❌ Masque visuel non créé")
            return False
        
        print("✅ Fichiers de masques créés")
        
        # Charger et vérifier le masque binaire
        binary_mask = np.array(Image.open(binary_path))
        unique_values = np.unique(binary_mask)
        print(f"Valeurs uniques dans le masque binaire: {unique_values}")
        
        # Vérifier que toutes les classes sont présentes
        expected_values = set(CLASS_MAP.values())
        found_values = set(unique_values) - {0}  # Exclure le fond
        
        if expected_values.issubset(found_values):
            print("✅ Toutes les classes présentes dans le masque binaire")
        else:
            missing = expected_values - found_values
            print(f"❌ Classes manquantes dans le masque binaire: {missing}")
            return False
        
        # Charger et vérifier le masque visuel
        visual_mask = np.array(Image.open(visual_path))
        print("Vérification des couleurs du masque visuel...")
        
        for class_value, expected_rgb in MASK_COLORS_RGB.items():
            class_pixels = (binary_mask == class_value)
            if np.any(class_pixels):
                # Vérifier les couleurs correspondantes dans le masque visuel
                visual_colors = visual_mask[class_pixels]
                unique_colors = np.unique(visual_colors.reshape(-1, 3), axis=0)
                
                if len(unique_colors) == 1:
                    actual_color = unique_colors[0].tolist()
                    if actual_color == expected_rgb:
                        print(f"✅ Classe {class_value}: couleur correcte {actual_color}")
                    else:
                        print(f"❌ Classe {class_value}: couleur incorrecte {actual_color}, attendu {expected_rgb}")
                        return False
                else:
                    print(f"❌ Classe {class_value}: couleurs incohérentes {unique_colors}")
                    return False
        
        print("✅ Toutes les couleurs du masque polygonal sont correctes")
        
        # Nettoyage
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'export polygonal: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_standard_vs_polygon_comparison():
    """Compare les exports standard et polygonal pour vérifier les différences."""
    print("\n=== Comparaison masques standard vs polygonal ===")
    
    try:
        # Créer un répertoire temporaire
        temp_dir = tempfile.mkdtemp()
        
        # Créer des masques de test
        mask_std = np.zeros((50, 50), dtype=np.uint8)
        mask_poly = np.zeros((50, 50), dtype=np.uint8)
        
        # Ajouter des régions pour chaque classe
        mask_std[10:20, 10:20] = 1  # frontwall
        mask_std[25:35, 10:20] = 2  # backwall
        mask_std[10:20, 25:35] = 3  # flaw
        mask_std[25:35, 25:35] = 4  # indication
        
        # Pour le masque polygonal, utiliser generate_profile_mask
        for class_value in [1, 2, 3, 4]:
            # Créer un masque binaire pour cette classe
            binary_region = (mask_std == class_value).astype(np.uint8)
            if np.any(binary_region):
                # Générer le profil
                profile = generate_profile_mask(binary_region, class_value)
                # Ajouter au masque polygonal
                mask_poly = np.maximum(mask_poly, profile)
        
        # Sauvegarder les deux types
        save_all_masks(mask_std, mask_poly, temp_dir, "comparison_test.png", "standard")
        save_all_masks(mask_std, mask_poly, temp_dir, "comparison_test.png", "polygon")
        
        print("✅ Comparaison standard vs polygonal terminée")
        
        # Nettoyage
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la comparaison: {str(e)}")
        return False


def main():
    """Fonction principale."""
    print("Tests spécifiques pour les masques polygonaux\n")
    
    success = True
    
    # Test 1: Génération des masques de profil
    if not test_profile_mask_generation():
        success = False
    
    # Test 2: Export des masques polygonaux
    if not test_polygon_mask_export():
        success = False
    
    # Test 3: Comparaison standard vs polygonal
    if not test_standard_vs_polygon_comparison():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("✅ TOUS LES TESTS POLYGONAUX SONT PASSÉS")
        print("Les masques polygonaux exportent maintenant les bonnes couleurs!")
    else:
        print("❌ CERTAINS TESTS POLYGONAUX ONT ÉCHOUÉ")
        print("Des corrections supplémentaires sont nécessaires.")
    
    return success


if __name__ == "__main__":
    main()
