"""
Fonctions utilitaires pour la conversion et normalisation de données.
"""
import numpy as np

def remove_isolated_columns(x_vals, max_gap=5):
    """
    Supprime les colonnes isolées dans un tableau.
    
    Args:
        x_vals: Valeurs X des colonnes
        max_gap: <PERSON><PERSON><PERSON> maximum autorisé entre colonnes
        
    Returns:
        Colonnes nettoyées
    """
    if len(x_vals) < 2:
        return x_vals
    diffs = np.diff(x_vals)
    keep = np.ones_like(x_vals, dtype=bool)
    keep[1:] &= diffs <= max_gap
    keep[:-1] &= diffs <= max_gap
    return x_vals[keep]

def normalize_polygon_coords(pts, margin):
    """
    Normalise les coordonnées d'un polygone en tenant compte de la marge.
    
    Args:
        pts: Liste de points (x,y)
        margin: Marge à soustraire
        
    Returns:
        Points normalisés
    """
    return [(x - margin, y - margin) for (x, y) in pts]

def apply_binary_threshold(gray_img, threshold):
    """
    Applique un seuil binaire à une image en niveaux de gris.
    
    Args:
        gray_img: Image en niveaux de gris
        threshold: Seuil
        
    Returns:
        Masque binaire
    """
    return (gray_img < threshold).astype(np.uint8) 