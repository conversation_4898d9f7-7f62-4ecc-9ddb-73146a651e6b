"""
Configuration du logging de l'application.
"""
import logging

def configure_logging(log_level: str = 'INFO', log_file: str = None) -> None:
    """
    Configure le logger racine.

    Args:
        log_level: Niveau de log (DEBUG, INFO, WARNING, ERROR)
        log_file: <PERSON><PERSON><PERSON> vers le fichier de log (optionnel)
    """
    level = getattr(logging, log_level.upper(), logging.INFO)
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    logging.basicConfig(
        level=level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=handlers
    ) 